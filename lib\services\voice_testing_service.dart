import 'package:flutter/material.dart';
import 'package:phoenix/services/voice_assistant_service.dart';
import 'package:phoenix/models/voice_command_model.dart';

/// Service for testing voice commands during development
/// This simulates what Google Assistant would send to the app
class VoiceTestingService {
  static final VoiceTestingService _instance = VoiceTestingService._internal();
  factory VoiceTestingService() => _instance;
  VoiceTestingService._internal();

  final VoiceAssistantService _voiceAssistant = VoiceAssistantService();

  /// Test voice commands that simulate Google Assistant input
  Future<void> testVoiceCommand(String command) async {
    debugPrint('Voice Testing: Simulating command - $command');
    
    VoiceCommand? voiceCommand;
    
    // Parse common test commands
    final lowerCommand = command.toLowerCase();
    
    if (lowerCommand.contains('buy') && lowerCommand.contains('stock')) {
      // Example: "buy 10 shares of Apple stock"
      voiceCommand = VoiceCommand(
        action: VoiceCommandAction.buy,
        symbol: _extractSymbol(command) ?? 'TCS',
        quantity: _extractQuantity(command) ?? 10,
        orderType: VoiceOrderType.market,
        originalText: command,
      );
    } else if (lowerCommand.contains('sell') && lowerCommand.contains('stock')) {
      // Example: "sell 5 shares of Tesla stock"
      voiceCommand = VoiceCommand(
        action: VoiceCommandAction.sell,
        symbol: _extractSymbol(command) ?? 'TSLA',
        quantity: _extractQuantity(command) ?? 5,
        orderType: VoiceOrderType.market,
        originalText: command,
      );
    } else if (lowerCommand.contains('portfolio') || lowerCommand.contains('holdings')) {
      // Example: "show my portfolio"
      voiceCommand = VoiceCommand(
        action: VoiceCommandAction.checkPortfolio,
        originalText: command,
      );
    } else if (lowerCommand.contains('price') || lowerCommand.contains('quote')) {
      // Example: "get Apple stock price"
      voiceCommand = VoiceCommand(
        action: VoiceCommandAction.getQuote,
        symbol: _extractSymbol(command) ?? 'TCS',
        originalText: command,
      );
    }

    if (voiceCommand != null) {
      await _voiceAssistant.handleAssistantIntent(voiceCommand.toJson());
    } else {
      // Create an unknown command to trigger proper error handling through the UI
      final unknownCommand = VoiceCommand(
        action: VoiceCommandAction.unknown,
        originalText: command,
      );
      await _voiceAssistant.handleAssistantIntent(unknownCommand.toJson());
    }
  }

  /// Extract stock symbol from command text
  String? _extractSymbol(String command) {
    final symbolMap = {
      'apple': 'TCS',
      'microsoft': 'MSFT',
      'tesla': 'TSLA',
      'google': 'GOOGL',
      'amazon': 'AMZN',
      'meta': 'META',
      'netflix': 'NFLX',
      'nvidia': 'NVDA',
      'amd': 'AMD',
    };

    for (final entry in symbolMap.entries) {
      if (command.toLowerCase().contains(entry.key)) {
        return entry.value;
      }
    }

    // Look for stock symbols (3-5 uppercase letters)
    final symbolRegex = RegExp(r'\b([A-Z]{3,5})\b');
    final match = symbolRegex.firstMatch(command);
    return match?.group(1);
  }

  /// Extract quantity from command text
  int? _extractQuantity(String command) {
    final quantityRegex = RegExp(r'(\d+)\s*(?:shares?|stocks?)');
    final match = quantityRegex.firstMatch(command);
    if (match != null) {
      return int.tryParse(match.group(1)!);
    }

    // Look for any number in the command
    final numberRegex = RegExp(r'\b(\d+)\b');
    final numberMatch = numberRegex.firstMatch(command);
    return numberMatch != null ? int.tryParse(numberMatch.group(1)!) : null;
  }

  /// Get predefined test commands for easy testing
  List<String> get testCommands => [
    'buy 10 shares of Apple stock',
    'sell 5 shares of Tesla stock',
    'show my portfolio',
    'get Apple stock price',
    'buy 20 shares of Microsoft stock',
    'sell all my Google stock',
    'check my holdings',
    'what is the price of Netflix',
  ];

  /// Test all predefined commands
  Future<void> runAllTests() async {
    for (final command in testCommands) {
      debugPrint('Testing: $command');
      await testVoiceCommand(command);
      await Future.delayed(const Duration(seconds: 2)); // Wait between tests
    }
  }
}