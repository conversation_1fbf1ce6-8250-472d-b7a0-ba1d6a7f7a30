import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/services/voice_assistant_service.dart';
import 'package:phoenix/models/voice_command_model.dart';
import 'package:phoenix/features/orders/bloc/orders_bloc.dart';
import 'package:phoenix/features/orders/model/order_form_model.dart';
import 'package:phoenix/features/portfolio_data/bloc/portfolio_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/security_list/bloc/security_list_bloc.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/services/security_list_search_service.dart';
import 'package:phoenix/utils/http_service.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/features/conversation/bloc/conversation_bloc.dart';
import 'package:phoenix/features/conversation/bloc/conversation_event.dart';
import 'package:phoenix/features/broker_account_strategy_selection/bloc/broker_account_strategy_selection_bloc.dart';
import 'package:phoenix/widgets/broker_account_strategy_selector/broker_account_strategy_modal.dart';
import 'dart:convert';

/// Widget that handles voice commands and integrates with the app's order system
class VoiceCommandHandler extends StatefulWidget {
  final Widget child;

  const VoiceCommandHandler({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<VoiceCommandHandler> createState() => _VoiceCommandHandlerState();
}

class _VoiceCommandHandlerState extends State<VoiceCommandHandler> {
  final VoiceAssistantService _voiceAssistant = VoiceAssistantService();
  final HttpService _httpService = HttpService();


  // Track pending voice orders to send appropriate chat messages
  VoiceCommand? _pendingVoiceOrder;

  @override
  void initState() {
    super.initState();
    _initializeVoiceAssistant();
    _setupVoiceCommandListener();
    _setupOrdersListener();
  }

  Future<void> _initializeVoiceAssistant() async {
    await _voiceAssistant.initialize();

    // Ensure security list is loaded for voice commands
    await _ensureSecurityListLoaded();
  }

  Future<void> _ensureSecurityListLoaded() async {
    final securityState = context.read<SecurityListBloc>().state;
    if (securityState is! SecurityListLoaded) {
      debugPrint('VoiceCommandHandler: Security list not loaded, fetching...');

      // Start the fetch
      context.read<SecurityListBloc>().add(FetchSecurityListEvent());

      // Wait for it to load (with timeout)
      int attempts = 0;
      while (attempts < 10) {
        // Max 5 seconds wait
        await Future.delayed(const Duration(milliseconds: 500));
        final currentState = context.read<SecurityListBloc>().state;
        if (currentState is SecurityListLoaded) {
          debugPrint(
              'VoiceCommandHandler: Security list loaded with ${currentState.equityList.length} equities, ${currentState.featuresList.length} F&O');
          return;
        }
        attempts++;
      }
      debugPrint(
          'VoiceCommandHandler: Timeout waiting for security list to load');
    } else {
      debugPrint(
          'VoiceCommandHandler: Security list already loaded with ${securityState.equityList.length} equities, ${securityState.featuresList.length} F&O');
    }
  }

  void _setupVoiceCommandListener() {
    _voiceAssistant.commandStream.listen((command) {
      // Guard: if a conversation voice session is active (listening/paused/finalizing),
      // ignore background/early commands to prevent duplicate or premature actions.
      // However, allow processing commands since they are the result of the current session.
      final conversationBloc = context.read<ConversationBloc>();
      final state = conversationBloc.state;
      final shouldIgnore =
          state.isListening || state.isPaused || state.isFinalizingInput;

      if (shouldIgnore) {
        debugPrint(
            'VoiceCommandHandler: Ignoring command during active voice input: ${command.toString()}');
        return;
      }
      _handleVoiceCommand(command);
    });
  }

  void _setupOrdersListener() {
    // OrdersBloc state changes are now handled in the build method with BlocListener
    // This ensures proper chat messages are sent based on actual order results
  }

  Future<void> _handleVoiceCommand(VoiceCommand command) async {
    debugPrint(
        'VoiceCommandHandler: Received voice command: ${command.toString()}');
    debugPrint(
        'VoiceCommandHandler: Action: ${command.action}, Symbol: ${command.symbol}, Quantity: ${command.quantity}');

    switch (command.action) {
      case VoiceCommandAction.buy:
        await _handleBuyCommand(command);
        break;
      case VoiceCommandAction.sell:
        await _handleSellCommand(command);
        break;
      case VoiceCommandAction.checkPortfolio:
        await _handlePortfolioCommand(command);
        break;
      case VoiceCommandAction.getQuote:
        await _handleQuoteCommand(command);
        break;
      case VoiceCommandAction.unknown:
        await _handleUnknownCommand(command);
        break;
    }
  }

  Future<void> _handleBuyCommand(VoiceCommand command) async {
    debugPrint(
        'VoiceCommandHandler: Handling buy command for ${command.symbol}');

    if (!command.isComplete) {
      final missing = command.missingParameters;
      debugPrint(
          'VoiceCommandHandler: Command incomplete, missing: ${missing.join(', ')}');
      final message =
          'I need more information. Please specify the ${missing.join(' and ')}.';

      // Send message to chat screen
      if (mounted) {
        context.read<ConversationBloc>().add(CommandResponseReceived(
              response: message,
              shouldSpeak: true,
            ));
      }
      return;
    }

    if (mounted) {
      // Ensure security list is loaded before searching
      await _ensureSecurityListLoaded();

      // Find the security to get the correct zen_id
      debugPrint(
          'VoiceCommandHandler: Searching for security with symbol: ${command.symbol}');
      final security = await _findSecurityBySymbol(command.symbol!);

      if (security == null) {
        debugPrint(
            'VoiceCommandHandler: Security not found for symbol: ${command.symbol}');
        final errorMessage =
            'Sorry, I couldn\'t find the stock ${command.symbol}. Please check the symbol and try again.';

        // Send message to chat screen
        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: errorMessage,
                shouldSpeak: true,
              ));
        }
        return;
      }

      // Validate if the security is valid for trading
      if (!_isSecurityValidForTrading(security)) {
        debugPrint(
            'VoiceCommandHandler: Security ${command.symbol} is not valid for trading');
        final errorMessage =
            'Sorry, ${command.symbol} is not available for trading. Please try a different stock.';

        // Send message to chat screen
        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: errorMessage,
                shouldSpeak: true,
              ));
        }
        return;
      }

      debugPrint(
          'VoiceCommandHandler: Found valid security: ${security.name} with zen_id: ${security.zenId}');

      // Show account selection dialog
      if (mounted) {
        context.read<ConversationBloc>().add(CommandResponseReceived(
              response:
                  'Please select your trading account to place the order.',
              shouldSpeak: true,
            ));
       
      }

      final selectionResult = await _showAccountSelectionDialog();
      if (selectionResult == null) {
        if (mounted) {
          debugPrint(
              "Hereeeeeee");
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: 'Order cancelled. Account selection is required.',
                shouldSpeak: true,
                commandId: command.id,
              ));

        }
        return;
      }

      // Get auth state for client ID
      final authState = context.read<AuthBloc>().state;
      if (authState is! AuthAuthenticated) {
        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: 'Authentication required to place orders.',
                shouldSpeak: true,
              ));
        }
        return;
      }

      // Extract selection values
      final brokerName = selectionResult['brokerName'] as String?;
      final accountId = selectionResult['accountId'];
      final strategyId = selectionResult['strategyId'];

      // Handle "all" selections by using the first available option
      final credentials = authState.credentialsModel;
      int finalAccountId;
      int finalStrategyId;
      String finalBroker;

      if (accountId == "all" || accountId == null) {
        // Use first available account
        final firstBroker = credentials.brokers.first;
        finalAccountId = firstBroker.accounts.first.accountId;
        finalBroker = firstBroker.brokerName;
        finalStrategyId =
            firstBroker.accounts.first.strategies.first.strategyId;
      } else {
        finalAccountId =
            accountId is int ? accountId : int.parse(accountId.toString());
        finalBroker = brokerName ?? 'ZEN_BROKER';

        if (strategyId == "all" || strategyId == null) {
          // Find the account and use its first strategy
          final account = credentials.getStrategies(finalAccountId);
          finalStrategyId = account.first.strategyId;
        } else {
          finalStrategyId =
              strategyId is int ? strategyId : int.parse(strategyId.toString());
        }
      }

      // Create order using selected values
      final orderForm = OrderFormModel(
        clientId: credentials.clientId,
        accountId: finalAccountId,
        strategyId: finalStrategyId,
        broker: finalBroker,
        exchange: 'NSE', // TODO: Determine from symbol
        transactionType: 'BUY',
        quantity: command.quantity!,
        product: 'CNC', // TODO: Get from user preference
        validity: 'DAY',
        orderType:
            _convertOrderType(command.orderType ?? VoiceOrderType.market),
        methodType: 'POST',
        zenId: security.zenId,
      );

      debugPrint(
          'VoiceCommandHandler: Placing order with zen_id: ${security.zenId}, accountId: $finalAccountId, strategyId: $finalStrategyId');

      // Store the pending order to track it
      _pendingVoiceOrder = command;
      debugPrint(
          '🔥 VoiceCommandHandler: Set pending voice order for BUY: ${command.symbol}');

      // Send processing message to chat screen first
      final processingMessage =
          'Submitting your buy order for ${command.quantity} shares of ${security.name}...';

      if (mounted) {
        context.read<ConversationBloc>().add(CommandResponseReceived(
              response: processingMessage,
              shouldSpeak: true,
            ));

        // Dispatch to OrdersBloc
        context.read<OrdersBloc>().add(PlaceOrderEvent(orderForm));
      }
    }
  }

  Future<void> _handleSellCommand(VoiceCommand command) async {
    debugPrint(
        'VoiceCommandHandler: Handling sell command for ${command.symbol}');

    if (!command.isComplete) {
      final missing = command.missingParameters;
      debugPrint(
          'VoiceCommandHandler: Command incomplete, missing: ${missing.join(', ')}');
      final message =
          'I need more information. Please specify the ${missing.join(' and ')}.';

      // Send message to chat screen
      if (mounted) {
        context.read<ConversationBloc>().add(CommandResponseReceived(
              response: message,
              shouldSpeak: true,
            ));
      }
      return;
    }

    if (mounted) {
      // Ensure security list is loaded before searching
      await _ensureSecurityListLoaded();

      // Find the security to get the correct zen_id
      debugPrint(
          'VoiceCommandHandler: Searching for security with symbol: ${command.symbol}');
      final security = await _findSecurityBySymbol(command.symbol!);

      if (security == null) {
        debugPrint(
            'VoiceCommandHandler: Security not found for symbol: ${command.symbol}');
        final errorMessage =
            'Sorry, I couldn\'t find the stock ${command.symbol}. Please check the symbol and try again.';

        // Send message to chat screen
        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: errorMessage,
                shouldSpeak: true,
              ));
        }
        return;
      }

      // Validate if the security is valid for trading
      if (!_isSecurityValidForTrading(security)) {
        debugPrint(
            'VoiceCommandHandler: Security ${command.symbol} is not valid for trading');
        final errorMessage =
            'Sorry, ${command.symbol} is not available for trading. Please try a different stock.';

        // Send message to chat screen
        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: errorMessage,
                shouldSpeak: true,
              ));
        }
        return;
      }

      debugPrint(
          'VoiceCommandHandler: Found valid security: ${security.name} with zen_id: ${security.zenId}');

      // Show account selection dialog
      if (mounted) {
        context.read<ConversationBloc>().add(CommandResponseReceived(
              response:
                  'Please select your trading account to place the order.',
              shouldSpeak: true,
            ));
      }

      final selectionResult = await _showAccountSelectionDialog();
      if (selectionResult == null) {
        if (mounted) {
          debugPrint(
              "Here");
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: 'Order cancelled. Account selection is required.',
                shouldSpeak: true,
                commandId: command.id,
              ));
        }
        return;
      }

      // Get auth state for client ID
      final authState = context.read<AuthBloc>().state;
      if (authState is! AuthAuthenticated) {
        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: 'Authentication required to place orders.',
                shouldSpeak: true,
              ));
        }
        return;
      }

      // Extract selection values
      final brokerName = selectionResult['brokerName'] as String?;
      final accountId = selectionResult['accountId'];
      final strategyId = selectionResult['strategyId'];

      // Handle "all" selections by using the first available option
      final credentials = authState.credentialsModel;
      int finalAccountId;
      int finalStrategyId;
      String finalBroker;

      if (accountId == "all" || accountId == null) {
        // Use first available account
        final firstBroker = credentials.brokers.first;
        finalAccountId = firstBroker.accounts.first.accountId;
        finalBroker = firstBroker.brokerName;
        finalStrategyId =
            firstBroker.accounts.first.strategies.first.strategyId;
      } else {
        finalAccountId =
            accountId is int ? accountId : int.parse(accountId.toString());
        finalBroker = brokerName ?? 'ZEN_BROKER';

        if (strategyId == "all" || strategyId == null) {
          // Find the account and use its first strategy
          final account = credentials.getStrategies(finalAccountId);
          finalStrategyId = account.first.strategyId;
        } else {
          finalStrategyId =
              strategyId is int ? strategyId : int.parse(strategyId.toString());
        }
      }

      // Create order using selected values
      final orderForm = OrderFormModel(
        clientId: credentials.clientId,
        accountId: finalAccountId,
        strategyId: finalStrategyId,
        broker: finalBroker,
        exchange: 'NSE', // TODO: Determine from symbol
        transactionType: 'SELL',
        quantity: command.quantity!,
        product: 'CNC', // TODO: Get from user preference
        validity: 'DAY',
        orderType:
            _convertOrderType(command.orderType ?? VoiceOrderType.market),
        methodType: 'POST',
        zenId: security.zenId,
      );

      debugPrint(
          'VoiceCommandHandler: Placing order with zen_id: ${security.zenId}, accountId: $finalAccountId, strategyId: $finalStrategyId');

      // Store the pending order to track it
      _pendingVoiceOrder = command;
      debugPrint(
          '🔥 VoiceCommandHandler: Set pending voice order for SELL: ${command.symbol}');

      // Send processing message to chat screen first
      final processingMessage =
          'Submitting your sell order for ${command.quantity} shares of ${security.name}...';

      if (mounted) {
        context.read<ConversationBloc>().add(CommandResponseReceived(
              response: processingMessage,
              shouldSpeak: true,
            ));

        // Dispatch to OrdersBloc
        context.read<OrdersBloc>().add(PlaceOrderEvent(orderForm));
      }
    }
  }

  Future<void> _handlePortfolioCommand(VoiceCommand command) async {
    final message = 'Opening your portfolio now.';

    // Send message to chat screen
    if (mounted) {
      context.read<ConversationBloc>().add(CommandResponseReceived(
            response: message,
            shouldSpeak: true,
          ));
    }

    // Trigger portfolio data fetch before navigation
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated && mounted) {
      context
          .read<PortfolioBloc>()
          .add(FetchPortfolio(authState.credentialsModel.clientId));

      // Navigate to portfolio screen
      Navigator.of(context).pushNamed('/portfolio');
    } else if (mounted) {
      final errorMessage = 'Please log in first to view your portfolio.';

      // Send error message to chat screen
      context.read<ConversationBloc>().add(CommandResponseReceived(
            response: errorMessage,
            shouldSpeak: true,
          ));
    }
  }

  Future<void> _handleQuoteCommand(VoiceCommand command) async {
    if (command.symbol == null) {
      final message = 'Please specify which stock you\'d like a quote for.';

      // Send message to chat screen
      if (mounted) {
        context.read<ConversationBloc>().add(CommandResponseReceived(
              response: message,
              shouldSpeak: true,
            ));
      }
      return;
    }

    final loadingMessage = 'Getting the current price for ${command.symbol}.';

    // Send loading message to chat screen
    if (mounted) {
      context.read<ConversationBloc>().add(CommandResponseReceived(
            response: loadingMessage,
            shouldSpeak: true,
          ));
    }

    try {
      // Find the security by symbol to get zen_id
      final security = await _findSecurityBySymbol(command.symbol!);

      if (security == null) {
        final errorMessage =
            'Sorry, I couldn\'t find the stock ${command.symbol}. Please check the symbol and try again.';

        // Send message to chat screen
        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: errorMessage,
                shouldSpeak: true,
              ));
        }
        return;
      }

      // Validate if the security is valid for trading
      if (!_isSecurityValidForTrading(security)) {
        debugPrint(
            'VoiceCommandHandler: Security ${command.symbol} is not valid for quote');
        final errorMessage =
            'Sorry, ${command.symbol} is not available for quotes. Please try a different stock.';

        // Send message to chat screen
        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: errorMessage,
                shouldSpeak: true,
              ));
        }
        return;
      }

      // Fetch the latest price
      final price = await _fetchLatestPrice(security.zenId);

      if (price != null) {
        final successMessage =
            'The current price of ${security.name} is ${price.toStringAsFixed(2)} rupees.';

        // Send message to chat screen
        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: successMessage,
                shouldSpeak: true,
              ));
        }
      } else {
        final errorMessage =
            'Sorry, I couldn\'t get the current price for ${command.symbol}. Please try again later.';

        // Send message to chat screen
        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: errorMessage,
                shouldSpeak: true,
              ));
        }
      }
    } catch (e) {
      debugPrint('Error getting quote: $e');
      final errorMessage =
          'Sorry, there was an error getting the price for ${command.symbol}. Please try again later.';

      // Send message to chat screen
      if (mounted) {
        context.read<ConversationBloc>().add(CommandResponseReceived(
              response: errorMessage,
              shouldSpeak: true,
            ));
      }
    }
  }

  Future<void> _handleUnknownCommand(VoiceCommand command) async {
    final message =
        'I didn\'t understand that command. You can say things like "buy 10 shares of Apple" or "check my portfolio".';

    // Send message to chat screen
    if (mounted) {
      context.read<ConversationBloc>().add(CommandResponseReceived(
            response: message,
            shouldSpeak: true,
          ));
    }
  }

  String _convertOrderType(VoiceOrderType voiceOrderType) {
    switch (voiceOrderType) {
      case VoiceOrderType.market:
        return 'MARKET';
      case VoiceOrderType.limit:
        return 'LIMIT';
      case VoiceOrderType.stopLoss:
        return 'STANDALONE_SL_MARKET';
      case VoiceOrderType.unknown:
        return 'MARKET'; // Default to market order
    }
  }

  /// Show account selection dialog and return selected values
  Future<Map<String, dynamic>?> _showAccountSelectionDialog() async {
    final authState = context.read<AuthBloc>().state;
    if (authState is! AuthAuthenticated) {
      if (mounted) {
        context.read<ConversationBloc>().add(CommandResponseReceived(
              response: 'Please log in first to place orders.',
              shouldSpeak: true,
            ));
      }
      return null;
    }

    final credentials = authState.credentialsModel;

    // Show dialog with BlocProvider for BrokerAccountStrategySelectionBloc
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false,
      builder: (context) => BlocProvider(
        create: (context) => BrokerAccountStrategySelectionBloc(credentials),
        child: BrokerAccountStrategyModal(
          showSetAsDefault: false,
          onDefaultSet: (clientId) {},
        ),
      ),
    );

    return result;
  }

  /// Find security by symbol using the security search service
  Future<SecurityModel?> _findSecurityBySymbol(String symbol) async {
    debugPrint('VoiceCommand: Searching for symbol: "$symbol"');

    final securityState = context.read<SecurityListBloc>().state;

    if (securityState is! SecurityListLoaded) {
      debugPrint('VoiceCommand: Security list not loaded');
      return null;
    }

    debugPrint(
        'VoiceCommand: Security list loaded - Equities: ${securityState.equityList.length}, F&O: ${securityState.featuresList.length}');

    // Try multiple search approaches for better matching

    // 1. Direct symbol matching in equities
    final directEquityMatch = securityState.equityList
        .where((s) => s.tradingSymbol.toUpperCase() == symbol.toUpperCase())
        .toList();

    if (directEquityMatch.isNotEmpty) {
      debugPrint(
          'VoiceCommand: Found direct equity match: ${directEquityMatch.first.tradingSymbol} (zen_id: ${directEquityMatch.first.zenId})');
      return directEquityMatch.first;
    }

    // 2. Name matching in equities (for company names)
    final nameEquityMatch = securityState.equityList
        .where((s) =>
            s.name.toUpperCase().contains(symbol.toUpperCase()) ||
            s.tradingSymbol.toUpperCase().contains(symbol.toUpperCase()))
        .toList();

    if (nameEquityMatch.isNotEmpty) {
      debugPrint(
          'VoiceCommand: Found equity name match: ${nameEquityMatch.first.tradingSymbol} (${nameEquityMatch.first.name}) (zen_id: ${nameEquityMatch.first.zenId})');
      return nameEquityMatch.first;
    }

    // 3. Use the search service for equities
    // final equityResults = <SecurityModel>[];
    // SecurityListSearchService.optimizedSearch(
    //   symbol,
    //   context,
    //   'equity',
    //   () => equityResults.clear(),
    //   (results) => equityResults.addAll(results),
    // );

    // if (equityResults.isNotEmpty) {
    //   debugPrint('VoiceCommand: Found equity via search service: ${equityResults.first.tradingSymbol} (zen_id: ${equityResults.first.zenId})');
    //   return equityResults.first;
    // }

    // 4. Direct symbol matching in F&O
    final directFnoMatch = securityState.featuresList
        .where((s) => s.tradingSymbol.toUpperCase() == symbol.toUpperCase())
        .toList();

    if (directFnoMatch.isNotEmpty) {
      debugPrint(
          'VoiceCommand: Found direct F&O match: ${directFnoMatch.first.tradingSymbol} (zen_id: ${directFnoMatch.first.zenId})');
      return directFnoMatch.first;
    }

    // // 5. Use the search service for F&O
    // final fnoResults = <SecurityModel>[];
    // SecurityListSearchService.optimizedSearch(
    //   symbol,
    //   context,
    //   'fno',
    //   () => fnoResults.clear(),
    //   (results) => fnoResults.addAll(results),
    // );

    // if (fnoResults.isNotEmpty) {
    //   debugPrint('VoiceCommand: Found F&O via search service: ${fnoResults.first.tradingSymbol} (zen_id: ${fnoResults.first.zenId})');
    //   return fnoResults.first;
    // }

    debugPrint('VoiceCommand: No matches found for symbol: "$symbol"');

    // 6. Debug: Show some available symbols for troubleshooting
    final sampleEquities = securityState.equityList
        .take(5)
        .map((s) => '${s.tradingSymbol} (${s.name})')
        .join(', ');
    debugPrint('VoiceCommand: Sample available equities: $sampleEquities');

    return null;
  }

  /// Fetch latest price for a given zen_id
  Future<double?> _fetchLatestPrice(int zenId) async {
    try {
      final url = ApiPath.getLtpPrice(zenId);
      debugPrint('Fetching price from: $url');

      final response = await _httpService.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        debugPrint('Price response: $data');

        // Extract the latest price from the response
        if (data['latest_price'] != null) {
          return (data['latest_price'] as num).toDouble();
        }
      } else {
        debugPrint(
            'Failed to fetch price. Status code: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching latest price: $e');
    }

    return null;
  }

  /// Validate if a security is valid for trading
  bool _isSecurityValidForTrading(SecurityModel security) {
    // Check if zenId is valid (should be positive)
    if (security.zenId <= 0) {
      debugPrint('VoiceCommandHandler: Invalid zenId: ${security.zenId}');
      return false;
    }

    // Check if trading symbol is valid (should not be empty or "Not Found")
    if (security.tradingSymbol.isEmpty ||
        security.tradingSymbol.toUpperCase() == 'NOT FOUND') {
      debugPrint(
          'VoiceCommandHandler: Invalid trading symbol: ${security.tradingSymbol}');
      return false;
    }

    // Check if name is valid (should not be empty or "Not Found")
    if (security.name.isEmpty || security.name.toUpperCase() == 'NOT FOUND') {
      debugPrint(
          'VoiceCommandHandler: Invalid security name: ${security.name}');
      return false;
    }

    // Check if exchanges list is not empty
    if (security.exchanges.isEmpty) {
      debugPrint(
          'VoiceCommandHandler: No exchanges available for security: ${security.tradingSymbol}');
      return false;
    }

    // Check if instrument type is valid (should not be empty)
    if (security.instrumentType.isEmpty) {
      debugPrint(
          'VoiceCommandHandler: Invalid instrument type for security: ${security.tradingSymbol}');
      return false;
    }

    // Check if lot size is valid (should be positive)
    if (security.lotSize <= 0) {
      debugPrint(
          'VoiceCommandHandler: Invalid lot size: ${security.lotSize} for security: ${security.tradingSymbol}');
      return false;
    }

    // All validations passed
    debugPrint(
        'VoiceCommandHandler: Security ${security.tradingSymbol} is valid for trading');
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<OrdersBloc, OrdersState>(
      listener: (context, state) {
        _handleOrderStateChange(context, state);
      },
      child: widget.child,
    );
  }

  void _handleOrderStateChange(BuildContext context, OrdersState state) {
    debugPrint(
        '🔥 VoiceCommandHandler: Order state changed: ${state.runtimeType}');

    // Only handle order state changes if we have a pending voice order
    if (_pendingVoiceOrder == null) {
      debugPrint(
          '🔥 VoiceCommandHandler: No pending voice order, ignoring state change');
      return;
    }

    debugPrint(
        '🔥 VoiceCommandHandler: Processing order state for pending voice order: ${_pendingVoiceOrder!.action}');

    if (state is OrderPlaced) {
      final command = _pendingVoiceOrder!;
      final action = command.action == VoiceCommandAction.buy ? 'buy' : 'sell';

      debugPrint(
          '🔥 VoiceCommandHandler: Order placed with status: ${state.status}');

      if (state.status == "ACCEPTED") {
        final successMessage =
            'Your $action order for ${command.quantity} shares of ${command.symbol} has been placed successfully.';

        debugPrint(
            '🔥 VoiceCommandHandler: Sending success message to chat: $successMessage');

        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: successMessage,
                shouldSpeak: true,
              ));
        }
      } else {
        final errorMessage = 'Your $action order failed: ${state.message}';

        debugPrint(
            '🔥 VoiceCommandHandler: Sending error message to chat: $errorMessage');

        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: errorMessage,
                shouldSpeak: true,
              ));
        }
      }

      // Clear the pending order
      _pendingVoiceOrder = null;
    } else if (state is OrderError) {
      final command = _pendingVoiceOrder!;
      final action = command.action == VoiceCommandAction.buy ? 'buy' : 'sell';
      final errorMessage =
          'Failed to place your $action order: ${state.message}';

      debugPrint(
          '🔥 VoiceCommandHandler: Sending order error message to chat: $errorMessage');

      if (mounted) {
        context.read<ConversationBloc>().add(CommandResponseReceived(
              response: errorMessage,
              shouldSpeak: true,
            ));
      }

      // Clear the pending order
      _pendingVoiceOrder = null;
    }
  }
}
