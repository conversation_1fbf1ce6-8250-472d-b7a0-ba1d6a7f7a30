import 'dart:async';
import 'dart:io' show Platform;
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_speech/flutter_speech.dart';
import 'package:phoenix/services/voice_assistant_service.dart';

/// Thin wrapper around flutter_speech to capture voice and turn it into
/// VoiceAssistant intents for processing by VoiceAssistantService.
class SpeechRecognitionService {
  static final SpeechRecognitionService _instance = SpeechRecognitionService._internal();
  factory SpeechRecognitionService() => _instance;
  SpeechRecognitionService._internal();

  final SpeechRecognition _speech = SpeechRecognition();
  final VoiceAssistantService _assistant = VoiceAssistantService();

  bool _available = false;
  bool _listening = false;
  final String _localeId = 'en_US';

  // Enhanced session state for pause detection
  void Function(String)? _onPartial;
  void Function(String)? _onFinal;
  void Function()? _onListening;
  void Function()? _onPauseDetected;
  void Function()? _onFinalizing;
  void Function()? _onTimeout;
  String? _lastRecognizedText;
  Completer<String?>? _activeSession;
  
  // Pause detection state
  Timer? _pauseDetectionTimer;
  Timer? _timeoutTimer;
  DateTime? _lastSpeechTime;
  bool _hasSpeechInput = false;
  bool _isPaused = false;
  bool _isFinalizingInput = false;
  
  // Configuration constants
  static const Duration _pauseThreshold = Duration(seconds: 2, milliseconds: 500); // 2.5 seconds
  static const Duration _maxListeningDuration = Duration(seconds: 30);
  static const Duration _finalizeDelay = Duration(milliseconds: 800); // Wait before finalizing

  /// Exposed state
  bool get isAvailable => _available;
  bool get isListening => _listening;
  bool get isPaused => _isPaused;
  bool get isFinalizingInput => _isFinalizingInput;
  bool get hasSpeechInput => _hasSpeechInput;
  String get localeId => _localeId;
  
  /// Check current status for debugging
  Map<String, dynamic> get status => {
    'isAvailable': _available,
    'isListening': _listening,
    'isPaused': _isPaused,
    'isFinalizingInput': _isFinalizingInput,
    'hasSpeechInput': _hasSpeechInput,
    'localeId': _localeId,
    'hasActiveSession': _activeSession != null,
    'lastSpeechTime': _lastSpeechTime?.toIso8601String(),
  };

  /// Initialize the STT engine and request microphone permission
  Future<bool> initialize() async {
    try {
      debugPrint('SpeechRecognition: Starting initialization...');
      
      // Ask mic permission
      final micStatus = await Permission.microphone.request();
      if (!micStatus.isGranted) {
        debugPrint('SpeechRecognition: Microphone permission denied');
        return false;
      }
      debugPrint('SpeechRecognition: Microphone permission granted');

      // Handle speech recognition permission
      if (Platform.isAndroid) {
        // On Android, permission_handler can request speech (maps to RECORD_AUDIO)
        final speechStatus = await Permission.speech.request();
        if (!speechStatus.isGranted) {
          debugPrint('SpeechRecognition: Speech recognition permission denied (Android)');
          return false;
        }
        debugPrint('SpeechRecognition: Speech recognition permission granted (Android)');
      } else if (Platform.isIOS) {
        // On iOS, DO NOT rely on permission_handler for speech permission.
        // iOS will show the speech permission popup when activating the engine.
        debugPrint('SpeechRecognition: iOS will request speech permission via speech engine');
      }

      // Setup handlers
      _speech.setAvailabilityHandler((bool result) {
        _available = result;
        debugPrint('FlutterSpeech availability changed: $result');
        if (!result) {
          debugPrint('SpeechRecognition: Speech recognition became unavailable');
        }
      });

      _speech.setRecognitionStartedHandler(() {
        _listening = true;
        debugPrint('FlutterSpeech recognition started - now listening');
      });

      _speech.setRecognitionResultHandler((String text) {
        final trimmed = text.trim();
        debugPrint('FlutterSpeech result received: "$trimmed"');
        if (trimmed.isEmpty) return;
        
        _lastRecognizedText = trimmed;
        _hasSpeechInput = true;
        _lastSpeechTime = DateTime.now();
        
        // Reset pause state when new speech is detected
        if (_isPaused) {
          _isPaused = false;
          _onListening?.call();
        }
        
        _onPartial?.call(trimmed);
        _restartPauseDetection();
      });

      _speech.setRecognitionCompleteHandler((String result) {
        _listening = false;
        final text = result.isNotEmpty ? result : _lastRecognizedText;
        debugPrint('FlutterSpeech recognition complete: "$text"');
        if (text != null && text.isNotEmpty) {
          _finalizeRecognition(text);
        } else {
          debugPrint('FlutterSpeech: No text recognized');
          _activeSession?.complete(null);
          _activeSession = null;
        }
      });

      _speech.setErrorHandler(() {
        debugPrint('FlutterSpeech error occurred');
        _listening = false;
        if (_activeSession != null) {
          _activeSession?.complete(null);
          _activeSession = null;
        }
      });

      // Try to activate the speech engine
      debugPrint('SpeechRecognition: Attempting to activate speech engine...');
      _available = await _speech.activate(_localeId);
      debugPrint('SpeechRecognition: Speech engine activation result: $_available');

      if (_available) {
        debugPrint('SpeechRecognition: Successfully initialized');
      } else {
        debugPrint('SpeechRecognition: Failed to activate speech engine');
      }

      return _available;
    } catch (e) {
      debugPrint('SpeechRecognition init failed with exception: $e');
      _available = false;
      return false;
    }
  }

  /// Start listening with enhanced pause detection.
  /// Returns the final recognized text once completed.
  Future<String?> startListening({
    void Function(String partialText)? onPartial,
    void Function(String finalText)? onFinal,
    void Function()? onListening,
    void Function()? onPauseDetected,
    void Function()? onFinalizing,
    void Function()? onTimeout,
  }) async {
    debugPrint('SpeechRecognition: startListening called');
    
    if (_activeSession != null) {
      debugPrint('SpeechRecognition: Already listening, cancelling previous session');
      await stopListening();
    }

    // Check if we need to initialize or reinitialize
    if (!_available) {
      debugPrint('SpeechRecognition: Not available, attempting to initialize');
      final ok = await initialize();
      if (!ok) {
        debugPrint('SpeechRecognition: Initialization failed, cannot start listening');
        return null;
      }
    }

    // Double check availability after initialization
    if (!_available) {
      debugPrint('SpeechRecognition: Still not available after initialization');
      return null;
    }

    _onPartial = onPartial;
    _onFinal = onFinal;
    _onListening = onListening;
    _onPauseDetected = onPauseDetected;
    _onFinalizing = onFinalizing;
    _onTimeout = onTimeout;
    _lastRecognizedText = null;
    _activeSession = Completer<String?>();
    
    // Reset pause detection state
    _hasSpeechInput = false;
    _isPaused = false;
    _isFinalizingInput = false;
    _lastSpeechTime = null;
    _cancelTimers();

    debugPrint('SpeechRecognition: Initializing voice assistant...');
    await _assistant.initialize();

    try {
      // Add a small delay to ensure speech engine is ready
      debugPrint('SpeechRecognition: Waiting for speech engine to be ready...');
      await Future.delayed(const Duration(milliseconds: 200));
      
      debugPrint('SpeechRecognition: Calling _speech.listen()...');
      final ok = await _speech.listen();
      debugPrint('SpeechRecognition: _speech.listen() returned: $ok');
      
      if (!ok) {
        debugPrint('SpeechRecognition: listen() returned false - speech recognition failed to start');
        _activeSession?.complete(null);
        _activeSession = null;
        return null;
      }
      
      debugPrint('SpeechRecognition: Successfully started listening - waiting for speech input');
      
      // Start timeout timer
      _startTimeoutTimer();
      
      // Notify UI of listening state
      _onListening?.call();
      
    } catch (e) {
      debugPrint('SpeechRecognition: listen() threw exception: $e');
      _cancelTimers();
      _activeSession?.complete(null);
      _activeSession = null;
      return null;
    }

    return _activeSession!.future;
  }

  Future<void> stopListening() async {
    try {
      _cancelTimers();
      
      if (_listening) {
        await _speech.stop();
      }
      
      _listening = false;
      _isPaused = false;
      _isFinalizingInput = false;
      _hasSpeechInput = false;
      _lastSpeechTime = null;
      
      _activeSession?.complete(null);
      _activeSession = null;
      debugPrint('SpeechRecognition: Stopped listening');
    } catch (e) {
      debugPrint('SpeechRecognition stop failed: $e');
    }
  }

  Future<void> _finalizeRecognition(String text) async {
    try {
      debugPrint('SpeechRecognition: Finalizing recognition with text: "$text"');
      
      // Only call the final callback - let the UI layer handle command creation and processing
      _onFinal?.call(text);
      
      // Create intent for debugging purposes only
      final intent = _parseTextToIntent(text);
      debugPrint('SpeechRecognition: Parsed intent (for debugging): $intent');
      
      // IMPORTANT: Removed _assistant.handleAssistantIntent(intent) call to prevent duplicate orders
      // The VoiceCommandHandler will handle the actual command processing
      
    } catch (e) {
      debugPrint('SpeechRecognition finalize failed: $e');
    } finally {
      _activeSession?.complete(text);
      _activeSession = null;
      // Ensure we stop the engine
      try { await _speech.stop(); } catch (_) {}
    }
  }

  /// Enhanced NLP parser to infer action, symbol and quantity with better accuracy
  Map<String, dynamic> _parseTextToIntent(String text) {
    // Preprocess the text for better accuracy
    final preprocessedText = _preprocessText(text);
    final lower = preprocessedText.toLowerCase();
    debugPrint('SpeechRecognition: Original: "$text" -> Preprocessed: "$preprocessedText"');

    String action = 'unknown';
    
    // Enhanced action detection with phonetic matching and context awareness
    action = _detectAction(lower);
    
    final symbol = _extractSymbol(preprocessedText);
    final quantity = _extractQuantity(preprocessedText);

    debugPrint('SpeechRecognition: Detected - Action: $action, Symbol: $symbol, Quantity: $quantity');

    return <String, dynamic>{
      'action': action,
      'symbol': symbol,
      'quantity': quantity,
      'type': 'market',
      'originalText': text,
    };
  }

  /// Preprocess text to fix common speech recognition errors
  String _preprocessText(String text) {
    String processed = text;
    
    // Common speech-to-text corrections for trading terms
    final corrections = {
      // Action corrections
      r'\bby\b(?=\s+\d)': 'buy',  // "by" followed by number -> "buy"
      r'\bbye\b(?=\s+\d)': 'buy',
      r'\bbi\b(?=\s+\d)': 'buy',
      r'\bcell\b(?=\s+\d)': 'sell',
      r'\bsal\b(?=\s+\d)': 'sell',
      
      // Number corrections
      r'\bto\b(?=\s+shares?)': '2',
      r'\btoo\b(?=\s+shares?)': '2',
      r'\bfor\b(?=\s+shares?)': '4',
      r'\bfore\b(?=\s+shares?)': '4',
      r'\bate\b(?=\s+shares?)': '8',
      
      // Stock name corrections
      r'\btc\s*s\b': 'TCS',
      r'\bt\s*c\s*s\b': 'TCS',
      r'\btata\s*consultancy\b': 'TCS',
    };
    
    for (final entry in corrections.entries) {
      processed = processed.replaceAll(RegExp(entry.key, caseSensitive: false), entry.value);
    }
    
    return processed;
  }

  /// Enhanced action detection with phonetic matching and context awareness
  String _detectAction(String lowerText) {
    // Direct matches first
    if (lowerText.contains('buy ') || lowerText.startsWith('buy') || lowerText.endsWith(' buy')) {
      return 'buy';
    }
    if (lowerText.contains('sell ') || lowerText.startsWith('sell') || lowerText.endsWith(' sell')) {
      return 'sell';
    }
    
    // Context-aware detection for common misheard words
    // "by" often means "buy" when followed by numbers and stock terms
    if (lowerText.contains(' by ') || lowerText.startsWith('by ')) {
      // Check if it's followed by numbers and stock-related terms
      if (RegExp(r'\bby\s+(\d+|\w+)\s*(shares?|stocks?|of|tcs|infosys|reliance)', caseSensitive: false).hasMatch(lowerText)) {
        debugPrint('SpeechRecognition: Correcting "by" to "buy" based on context');
        return 'buy';
      }
    }
    
    // Phonetic alternatives for "buy"
    final buyAlternatives = ['by', 'bye', 'bi', 'bai', 'bui'];
    for (final alt in buyAlternatives) {
      final pattern = RegExp(r'\b' + alt + r'\s+(\d+|\w+)\s*(shares?|stocks?|of)', caseSensitive: false);
      if (pattern.hasMatch(lowerText)) {
        debugPrint('SpeechRecognition: Correcting "$alt" to "buy" based on trading context');
        return 'buy';
      }
    }
    
    // Phonetic alternatives for "sell"
    final sellAlternatives = ['cell', 'sal', 'seal', 'cel'];
    for (final alt in sellAlternatives) {
      final pattern = RegExp(r'\b' + alt + r'\s+(\d+|\w+)\s*(shares?|stocks?|of)', caseSensitive: false);
      if (pattern.hasMatch(lowerText)) {
        debugPrint('SpeechRecognition: Correcting "$alt" to "sell" based on trading context');
        return 'sell';
      }
    }
    
    // Portfolio and quote detection
    if (lowerText.contains('portfolio') || lowerText.contains('holdings') || lowerText.contains('my stocks')) {
      return 'checkPortfolio';
    }
    if (lowerText.contains('quote') || lowerText.contains('price') || lowerText.contains('cost') || lowerText.contains('rate')) {
      return 'getQuote';
    }
    
    return 'unknown';
  }

  String? _extractSymbol(String command) {
    final lowerCommand = command.toLowerCase();
    
    // Company name to symbol mapping
    final symbolMap = {
      'apple': 'AAPL',
      'microsoft': 'MSFT',
      'tesla': 'TSLA',
      'google': 'GOOGL',
      'amazon': 'AMZN',
      'meta': 'META',
      'netflix': 'NFLX',
      'nvidia': 'NVDA',
      'amd': 'AMD',
      'tcs': 'TCS',
      't c s': 'TCS',
      't.c.s': 'TCS',
      'tata consultancy services': 'TCS',
      'tata consultancy': 'TCS',
      'infosys': 'INFY',
      'wipro': 'WIPRO',
      'reliance': 'RELIANCE',
      'hdfc bank': 'HDFCBANK',
      'hdfc': 'HDFCBANK',
      'icici bank': 'ICICIBANK',
      'icici': 'ICICIBANK',
      'sbi': 'SBIN',
      'state bank of india': 'SBIN',
      'bharti airtel': 'BHARTIARTL',
      'airtel': 'BHARTIARTL',
      'asian paints': 'ASIANPAINT',
    };

    // Check company name mappings first
    for (final entry in symbolMap.entries) {
      if (lowerCommand.contains(entry.key)) {
        return entry.value;
      }
    }

    // Look for stock symbols (3-5 letters, case insensitive)
    final symbolRegex = RegExp(r'\b([a-zA-Z]{3,5})\b');
    final matches = symbolRegex.allMatches(command);
    for (final match in matches) {
      final symbol = match.group(1)!.toUpperCase();
      // Filter out common words that aren't stock symbols
      if (!['BUY', 'SELL', 'SHARES', 'SHARE', 'STOCK', 'STOCKS', 'THE', 'AND', 'FOR', 'WITH', 'THIS', 'THAT', 'FROM', 'INTO'].contains(symbol)) {
        return symbol;
      }
    }
    
    return null;
  }

  int? _extractQuantity(String command) {
    final lowerCommand = command.toLowerCase();
    
    // First try to find numbers explicitly followed by shares/stocks
    final quantityRegex = RegExp(r'(\d+)\s*(?:shares?|stocks?)', caseSensitive: false);
    final match = quantityRegex.firstMatch(lowerCommand);
    if (match != null) {
      final quantity = int.tryParse(match.group(1)!);
      debugPrint('SpeechRecognition: Found quantity with shares/stocks: $quantity');
      return quantity;
    }
    
    // Handle spelled-out numbers commonly misheard
    final spokenNumbers = {
      'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,
      'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10,
      'eleven': 11, 'twelve': 12, 'thirteen': 13, 'fourteen': 14, 'fifteen': 15,
      'sixteen': 16, 'seventeen': 17, 'eighteen': 18, 'nineteen': 19, 'twenty': 20,
      'twenty-one': 21, 'twenty-two': 22, 'twenty-three': 23, 'twenty-four': 24, 'twenty-five': 25,
      'thirty': 30, 'forty': 40, 'fifty': 50, 'sixty': 60, 'seventy': 70, 'eighty': 80, 'ninety': 90,
      'hundred': 100, 'thousand': 1000,
      // Common phonetic alternatives
      'too': 2, 'to': 2, 'for': 4, 'fore': 4, 'ate': 8,
    };
    
    for (final entry in spokenNumbers.entries) {
      if (lowerCommand.contains(entry.key)) {
        debugPrint('SpeechRecognition: Found spoken number "${entry.key}": ${entry.value}');
        return entry.value;
      }
    }
    
    // Look for any number in the command
    final numberRegex = RegExp(r'\b(\d+)\b');
    final numberMatch = numberRegex.firstMatch(command);
    if (numberMatch != null) {
      final quantity = int.tryParse(numberMatch.group(1)!);
      debugPrint('SpeechRecognition: Found numeric quantity: $quantity');
      return quantity;
    }
    
    debugPrint('SpeechRecognition: No quantity found in: "$command"');
    return null;
  }

  
  /// Start pause detection timer
  void _restartPauseDetection() {
    _pauseDetectionTimer?.cancel();
    
    _pauseDetectionTimer = Timer(_pauseThreshold, () {
      if (_listening && _hasSpeechInput && !_isPaused && !_isFinalizingInput) {
        debugPrint('SpeechRecognition: Pause detected - waiting for more speech');
        _isPaused = true;
        _onPauseDetected?.call();
        
        // After pause is detected, wait a bit longer before finalizing
        _startFinalizationTimer();
      }
    });
  }
  
  /// Start finalization timer after pause detection
  void _startFinalizationTimer() {
    Timer(_finalizeDelay, () {
      if (_listening && _isPaused && !_isFinalizingInput) {
        debugPrint('SpeechRecognition: Finalizing input after extended pause');
        _isFinalizingInput = true;
        _onFinalizing?.call();
        
        // Finalize the recognition
        _finalizeWithCurrentText();
      }
    });
  }
  
  /// Start timeout timer
  void _startTimeoutTimer() {
    _timeoutTimer = Timer(_maxListeningDuration, () {
      if (_listening) {
        debugPrint('SpeechRecognition: Timeout reached - stopping listening');
        _onTimeout?.call();
        
        // If we have speech input, finalize it; otherwise, stop
        if (_hasSpeechInput && _lastRecognizedText != null) {
          _finalizeWithCurrentText();
        } else {
          stopListening();
        }
      }
    });
  }
  
  /// Cancel all timers
  void _cancelTimers() {
    _pauseDetectionTimer?.cancel();
    _pauseDetectionTimer = null;
    _timeoutTimer?.cancel();
    _timeoutTimer = null;
  }
  
  /// Finalize recognition with current text
  Future<void> _finalizeWithCurrentText() async {
    if (_lastRecognizedText != null && _lastRecognizedText!.isNotEmpty) {
      await _finalizeRecognition(_lastRecognizedText!);
    } else {
      await stopListening();
    }
  }
}
